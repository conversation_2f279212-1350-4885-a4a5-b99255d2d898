#include "Texture2D.h"

#define STB_IMAGE_IMPLEMENTATION
#include "stb_image.h"

#include <filesystem>
#include <glad/glad.h>
#include <stdexcept>
#include <iostream>

Texture2D::Texture2D(const std::filesystem::path& imagePath, GLint param)
{
	// ## Create texture object ##
	// ## What kind of texture | how many textures | where to store the ID ##
	glCreateTextures(GL_TEXTURE_2D, 1, &m_texture);

	// ## Wrapping and filtering ##
	// ## How to wrap texture horizontally | vertically | minification filter | magnification filter ##
	glTextureParameteri(m_texture, GL_TEXTURE_WRAP_S, param);
	glTextureParameteri(m_texture, GL_TEXTURE_WRAP_T, param);
	glTextureParameteri(m_texture, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
	glTextureParameteri(m_texture, GL_TEXTURE_MAG_FILTER, GL_LINEAR);

	// ## Flip the image vertically during loading ##
	stbi_set_flip_vertically_on_load(true);
	// ## Load image data ##
	m_data = stbi_load(imagePath.string().c_str(), &m_width, &m_height, &m_channels, 0);

	if (m_data)
	{
		// Determine correct format from channel count
		GLenum storageFormat = GL_R8;
		GLenum subFormat = GL_RED;
		switch (m_channels)
		{
			case 1: storageFormat = GL_R8; subFormat = GL_RED; break;
			case 3: storageFormat = GL_RGB8; subFormat = GL_RGB; break;
			case 4: storageFormat = GL_RGBA8; subFormat = GL_RGBA; break;
			default: storageFormat = GL_RGB8; subFormat = GL_RGB; break;
		}

		// ## Allocate storage and upload image data to GPU ##
		glTextureStorage2D(m_texture, 1, storageFormat, m_width, m_height);
		glTextureSubImage2D(m_texture, 0, 0, 0, m_width, m_height, subFormat, GL_UNSIGNED_BYTE, m_data);
		// ## Generate mipmaps ##
		glGenerateTextureMipmap(m_texture);
	}
	else
	{
		throw std::runtime_error("Failed to load texture");
	}

	stbi_image_free(m_data);
}

Texture2D::~Texture2D()
{
	if (m_texture != 0)
	{
		glDeleteTextures(1, &m_texture);
		m_texture = 0;
	}
}

const void Texture2D::Bind(GLuint index) const
{
	// ## Bind the texture to texture unit 0 which is the sampler2D defined in the shader ##
	glBindTextureUnit(index, m_texture);
}

GLuint Texture2D::GetTexture() const
{
	return m_texture;
}
