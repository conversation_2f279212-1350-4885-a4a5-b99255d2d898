#include "Engine.h"

#include "../Debug/Debug.h"
#include "Scenes/Scene.h"
#include "Window/Window.h"

#define GLFW_INCLUDE_NONE
#include <glad/glad.h>
#include <GLFW/glfw3.h>

#include <stdexcept>

Engine::Engine()
{
	Init();
	m_window = std::make_unique<Window>(800, 600, "Aincrad Engine");
	m_scene = std::make_unique<Scene>();
	m_scene->Init(*m_window);
}

void Engine::Init()
{
	// ## GLAD: Load all OpenGL function pointers ##
	if (!gladLoadGLLoader((GLADloadproc) glfwGetProcAddress))
	{
		throw std::runtime_error("Failed to initialize GLAD");
	}
	// ## Debugging ##
	Debug::InitGLDebug();
	// ## Enable depth testing ##
	glEnable(GL_DEPTH_TEST);
	// ## Set the viewport ##
	const auto [width, height] = m_window->GetWindowSize();
	glViewport(0, 0, static_cast<int>(width), static_cast<int>(height));
	// ## Register framebuffer size callback ##
	glfwSetFramebufferSizeCallback(&m_window->get(), &Window::WindowResizeCallback);
}

void Engine::Update()
{
	while (m_isRunning && glfwWindowShouldClose(&m_window->get()))
	{
		m_window->BeginFrame();
		ProcessInput(&m_window->get(), m_scene->GetCamera());
		UpdateDeltaTime();

		

		m_window->EndFrame();
	}	
}

auto Engine::ProcessInput(GLFWwindow* window, Camera3D& camera) -> void
{
	if (glfwGetKey(window, GLFW_KEY_ESCAPE) == GLFW_PRESS)
		glfwSetWindowShouldClose(window, true);

	if (glfwGetKey(window, GLFW_KEY_W) == GLFW_PRESS)
		camera.MoveForward();
	if (glfwGetKey(window, GLFW_KEY_S) == GLFW_PRESS)
		camera.MoveBackward();
	if (glfwGetKey(window, GLFW_KEY_A) == GLFW_PRESS)
		camera.MoveLeft();
	if (glfwGetKey(window, GLFW_KEY_D) == GLFW_PRESS)
		camera.MoveRight();
}

void Engine::UpdateDeltaTime()
{
	float currentFrame = static_cast<float>(glfwGetTime());
	m_deltaTime = currentFrame - m_lastFrame;
	m_lastFrame = currentFrame;
}