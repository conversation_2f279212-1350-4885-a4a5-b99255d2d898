#include "Camera3D.h"

#include "glm/gtc/matrix_transform.hpp"
#include <glm/fwd.hpp>

Camera3D::Camera3D()
	: m_cameraPosition(0.0f, 0.0f, 3.0f)
	, m_cameraTarget(0.0f, 0.0f, 0.0f)
	, m_cameraDirection(glm::normalize(m_cameraPosition - m_cameraTarget))
	, m_upVector(0.0f, 1.0f, 0.0f)
	, m_cameraRight(glm::normalize(glm::cross(m_upVector, m_cameraDirection)))
	, m_cameraUp(glm::cross(m_cameraDirection, m_cameraRight))
	, m_cameraFront(0.0f, 0.0f, -1.0f)
{}

Camera3D::Camera3D(const glm::vec3& position)
	: Camera3D()
{}

const glm::vec3& Camera3D::GetPosition() const
{
	return m_cameraPosition;
}

void Camera3D::SetPosition(const glm::vec3& position)
{
	m_cameraPosition = position;
}

void Camera3D::SetPosition(float x, float y, float z)
{
	m_cameraPosition = {x, y, z};
}

const glm::mat4 Camera3D::GetViewMatrix() const
{
	return glm::lookAt(m_cameraPosition, m_cameraPosition + m_cameraFront, m_upVector);
}

void Camera3D::MoveForward()
{
	m_cameraPosition += m_cameraSpeed * m_cameraFront;
}

void Camera3D::MoveBackward()
{
	m_cameraPosition -= m_cameraSpeed * m_cameraFront;
}

void Camera3D::MoveLeft()
{
	m_cameraPosition -= glm::normalize(glm::cross(m_cameraFront, m_upVector)) * m_cameraSpeed;
}

void Camera3D::MoveRight()
{
	m_cameraPosition += glm::normalize(glm::cross(m_cameraFront, m_upVector)) * m_cameraSpeed;
}
