#include "VertexArray.h"

#include <glad/glad.h>
#include <GLFW/glfw3.h>

// ## VertexArray Creation ##
VertexArray::VertexArray(int nArrays)
{
	glCreateVertexArrays(nArrays, &m_id);
}
// ## Bind a VBO to a specific binding index with stride and offset ##
const void VertexArray::BindVertexBuffer(GLuint buffer, GLuint stride, GLuint index, GLuint offset) const
{
	glVertexArrayVertexBuffer(m_id, index, buffer, offset, stride);
}
// ## Bind an EBO to the VAO ##
const void VertexArray::BindElementBuffer(GLuint buffer) const
{
	glVertexArrayElementBuffer(m_id, buffer);
}
// ## Enable a vertex attribute at the given index from the shader - otherwhise VAO will not use it ##
const void VertexArray::EnableAttribute(GLuint index) const
{
	glEnableVertexArrayAttrib(m_id, index);
}
// ## Define the format of a vertex attribute at the given index from shader ##
// ## index = layout location in shader | size = number of components | type = data type | normalized = if to normalize | relativeOffset = offset from start ##
const void VertexArray::AttributeFormat(GLuint index, GLuint size, GLenum type, GLboolean normalized, GLuint relativeOffset) const
{
	glVertexArrayAttribFormat(m_id, index, size, type, normalized, relativeOffset);
}
// ## Link a vertex attribute to a specific binding index (which VBO to use) ##
// ## attribIndex = layout location in shader | bindingIndex = which VBO binding index to use ##
const void VertexArray::AttributeBinding(GLuint attribIndex, GLuint bindingIndex) const
{
	glVertexArrayAttribBinding(m_id, attribIndex, bindingIndex);
}
// ## Bind the VAO for use ##
// ## Not needed if using direct state access (DSA) functions ##
const void VertexArray::Bind() const
{
	glBindVertexArray(m_id);
}
// ## Helper function to setup a common vertex format: position (3 floats) and color (3 floats) ##
const void VertexArray::SetupPos3Color3(GLuint vbo, GLuint bindingIndex, GLuint positionAttribIndex, GLuint colorAttribIndex) const
{
	const GLuint stride = static_cast<GLuint>(6 * sizeof(float));
	const GLuint colorOffset = static_cast<GLuint>(3 * sizeof(float));

	BindVertexBuffer(vbo, stride);
	EnableAttribute(positionAttribIndex);
	EnableAttribute(colorAttribIndex);
	AttributeFormat(positionAttribIndex, 3, GL_FLOAT, GL_FALSE, 0);
	AttributeFormat(colorAttribIndex, 3, GL_FLOAT, GL_FALSE, colorOffset);
	AttributeBinding(positionAttribIndex, bindingIndex);
	AttributeBinding(colorAttribIndex, bindingIndex);
}
// ## Helper function to setup a common vertex format: position (3 floats), color (3 floats) and texture coordinates (2 floats) ##
const void VertexArray::SetupPos3Color3Tex2(GLuint vbo, GLuint ebo, GLuint bindingIndex, GLuint positionAttribIndex, GLuint colorAttribIndex, GLuint texCoordAttribIndex) const
{
	const GLuint stride = static_cast<GLuint>(8 * sizeof(float));
	const GLuint colorOffset = static_cast<GLuint>(3 * sizeof(float));
	const GLuint texCoordOffset = static_cast<GLuint>(6 * sizeof(float));

	BindVertexBuffer(vbo, stride);
	BindElementBuffer(ebo);
	EnableAttribute(positionAttribIndex);
	EnableAttribute(colorAttribIndex);
	EnableAttribute(texCoordAttribIndex);
	AttributeFormat(positionAttribIndex, 3, GL_FLOAT, GL_FALSE, 0);
	AttributeFormat(colorAttribIndex, 3, GL_FLOAT, GL_FALSE, colorOffset);
	AttributeFormat(texCoordAttribIndex, 2, GL_FLOAT, GL_FALSE, texCoordOffset);
	AttributeBinding(positionAttribIndex, bindingIndex);
	AttributeBinding(colorAttribIndex, bindingIndex);
	AttributeBinding(texCoordAttribIndex, bindingIndex);
}

// ## Helper function to setup a common vertex format: position (3 floats) and texture coordinates (2 floats) ##
const void VertexArray::SetupPos3Tex2(GLuint vbo, GLuint bindingIndex, GLuint positionAttribIndex, GLuint texCoordAttribIndex) const
{
	const GLuint stride = static_cast<GLuint>(5 * sizeof(float));
	const GLuint texCoordOffset = static_cast<GLuint>(3 * sizeof(float));
	BindVertexBuffer(vbo, stride);
	EnableAttribute(positionAttribIndex);
	EnableAttribute(texCoordAttribIndex);
	AttributeFormat(positionAttribIndex, 3, GL_FLOAT, GL_FALSE, 0);
	AttributeFormat(texCoordAttribIndex, 2, GL_FLOAT, GL_FALSE, texCoordOffset);
	AttributeBinding(positionAttribIndex, bindingIndex);
	AttributeBinding(texCoordAttribIndex, bindingIndex);
}
