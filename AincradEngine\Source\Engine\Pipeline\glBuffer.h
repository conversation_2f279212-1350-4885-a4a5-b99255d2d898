#pragma once

#include <cstddef>
#include <glad/glad.h>
#include <GLFW/glfw3.h>
#include <vector>

typedef unsigned int GLuint;

class glBuffer
{
public:
	glBuffer() = default;

	// ## Create a buffer with specified size and data ##
	template<typename T>
	glBuffer(std::size_t size, const std::vector<T>& data, int nBuffers = 1)
		: m_id(0)
	{
		glCreateBuffers(nBuffers, &m_id);
		glNamedBufferStorage(m_id, sizeof(T) * size, data.data(), GL_DYNAMIC_STORAGE_BIT);
	}

	// ## Return the underlying OpenGL buffer ID ##
	GLuint GetBuffer() const;

private:
	GLuint m_id;
};


