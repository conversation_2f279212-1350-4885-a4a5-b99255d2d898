#include "Scene.h"

#include "../Window/Window.h"

#define GLFW_INCLUDE_NONE
#include <GLFW/glfw3.h>

#include <glm/fwd.hpp>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>

Scene::Scene()
	: m_camera(glm::vec3(0.0f, 0.0f, 3.0f))
	, m_mesh(m_vertices)
	, m_shader()
	, m_texture("Resources/Textures/container.jpg", GL_REPEAT)
	, m_texture2("Resources/Textures/awesomeface.png", GL_CLAMP_TO_EDGE)
	, m_projection()
	, m_model()
{
}

void Scene::Init(const Window& window)
{
	// ## Compile and link shaders ##
	m_shader.CompileAndLink();
	m_shader.Use();
	m_shader.setInt("ourTexture", 0);
	m_shader.setInt("ourTexture2", 1);

	m_texture.Bind(0);
	m_texture2.Bind(1);

	m_projection = glm::perspective(glm::radians(45.0f), window.GetWindowSize().first / window.GetWindowSize().second, 0.1f, 100.0f);
	m_shader.setMat4("projection", glm::value_ptr(m_projection));
}

void Scene::Update(float deltaTime)
{
	m_shader.setMat4("view", glm::value_ptr(m_camera.GetViewMatrix()));

	for (auto& position : m_cubePositions)
	{
		glm::mat4 model = glm::mat4(1.0f);
		model = glm::translate(model, position);
		float angle = 20.0f * static_cast<float>(&position - &m_cubePositions.at(0));
		model = glm::rotate(model, glm::radians(angle) + static_cast<float>(glfwGetTime()) * glm::radians(50.0f), glm::vec3(1.0f, 0.3f, 0.5f));
		m_shader.setMat4("model", glm::value_ptr(model));
		m_mesh.Draw();
	}
}

Camera3D& Scene::GetCamera()
{
	return m_camera;
}