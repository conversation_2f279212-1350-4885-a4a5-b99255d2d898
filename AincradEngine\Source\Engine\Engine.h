#pragma once

#include "Camera/Camera3D.h"
#include <memory>

struct GLFWwindow;
class Window;
class Scene;

class Engine
{
public:
	Engine();
	~Engine() = default;

	auto Init() -> void;
	auto Update() -> void;
	auto ProcessInput(GLFWwindow* window, Camera3D& camera) -> void;

private:
	auto UpdateDeltaTime() -> void;

	float m_deltaTime{0.0f};
	float m_lastFrame{0.0f};
	bool m_isRunning{false};

	std::unique_ptr<Window> m_window;
	std::unique_ptr<Scene> m_scene;
};