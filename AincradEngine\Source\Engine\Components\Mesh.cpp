#include "Mesh.h"
#include <glad/glad.h>
#include <vector>

Mesh::Mesh(const std::vector<float>& vertices)
    : m_VBO(vertices.size(), vertices)
    , m_VAO()
	, m_vertexCount(static_cast<GLsizei>(vertices.size() / 3))
	, m_indexCount(0)
{
	m_VAO.SetupPos3Tex2(m_VBO.GetBuffer());
    m_VAO.Bind();
}

Mesh::Mesh(const std::vector<float>& vertices, const std::vector<unsigned int>& indices)
    : m_VBO(vertices.size(), vertices)
    , m_EBO(indices.size(), indices)
    , m_VAO()
	, m_vertexCount(static_cast<GLsizei>(vertices.size() / 3))
	, m_indexCount(static_cast<GLsizei>(indices.size()))
{
    m_VAO.SetupPos3Color3Tex2(m_VBO.GetBuffer(), m_EBO.GetBuffer());
    m_VAO.Bind();
}

const void Mesh::Draw() const
{
    if (m_indexCount > 0)
    {
        glDrawElements(GL_TRIANGLES, m_indexCount, GL_UNSIGNED_INT, 0);
    }
    else
    {
        glDrawArrays(GL_TRIANGLES, 0, m_vertexCount);
    }
}
