#pragma once

#include <glad/glad.h>

typedef unsigned int GLuint;

class VertexArray
{
public:
	VertexArray(int nArrays = 1);
	auto BindVertexBuffer(GLuint buffer, GLuint stride, GLuint index = 0, GLuint offset = 0) const -> const void;
	auto BindElementBuffer(GLuint buffer) const -> const void;
	auto EnableAttribute(GLuint index) const-> const void;
	auto AttributeFormat(GLuint index, GLuint size, GLenum type = GL_FLOAT, G<PERSON><PERSON>lean normalized = GL_FALSE, GLuint relativeOffset = 0) const -> const void;
	auto AttributeBinding(GLuint attribIndex = 0, GLuint bindingIndex = 0) const -> const void;
	auto Bind() const -> const void;
	auto SetupPos3Color3(GLuint vbo, GLuint bindingIndex = 0, GLuint positionAttribIndex = 0, GLuint colorAttribIndex = 1) const -> const void;
	auto SetupPos3Color3Tex2(GLuint vbo, GLuint ebo, GLuint bindingIndex = 0, GLuint positionAttribIndex = 0, GLuint colorAttribIndex = 1, GLuint texCoordAttribIndex = 2) const -> const void;
	auto SetupPos3Tex2(GLuint vbo, GLuint bindingIndex = 0, GLuint positionAttribIndex = 0, GLuint texCoordAttribIndex = 2) const -> const void;
	auto get() const -> GLuint;

private:
	GLuint m_id;
};

