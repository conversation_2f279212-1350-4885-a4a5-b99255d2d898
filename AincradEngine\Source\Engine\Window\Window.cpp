#include "Window.h"

#include <glad/glad.h>
#define GLFW_INCLUDE_NONE
#include <GLFW/glfw3.h>
#include <memory>
#include <stdexcept>
#include <string>
#include <utility>

Window::Window(int width, int heigth, const std::string&)
	: m_window(nullptr, glfwDestroyWindow)
	, m_width(width)
	, m_heigth(heigth)
	, m_title()
{
	WindowSettings();

	m_window.reset(glfwCreateWindow(m_width, m_heigth, m_title.c_str(), nullptr, nullptr));

	if (m_window == nullptr)
	{
		throw std::runtime_error("Failed to create GLFW window");
		glfwTerminate();
	}
	glfwMakeContextCurrent(m_window.get());
}

Window::~Window()
{
	glfwDestroyWindow(m_window.get());
	glfwTerminate();
}

void Window::BeginFrame()
{
	// ## Clear the color and depth buffer ##
	glClearColor(0.2f, 0.3f, 0.3f, 1.0f);
	glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
}

void Window::EndFrame()
{
	// ## GLFW: Swap buffers and poll IO events (keys pressed/released, mouse moved etc.) ##
	glfwSwapBuffers(m_window.get());
	glfwPollEvents();
}

void Window::WindowSettings()
{
	// ## GLFW: Initialize and Configure ##
	glfwInit();
	glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 4);
	glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 5);
	glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
	glfwWindowHint(GLFW_OPENGL_DEBUG_CONTEXT, GLFW_TRUE);
}

void Window::WindowResizeCallback(GLFWwindow* window, int width, int height)
{
	glViewport(0, 0, width, height);
}

std::pair<float, float> Window::GetWindowSize() const
{
	return { m_width, m_heigth };
}

GLFWwindow& Window::get() const
{
	return *m_window;
}
