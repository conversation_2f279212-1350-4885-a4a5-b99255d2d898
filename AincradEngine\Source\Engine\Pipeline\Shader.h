#pragma once

#include <string>
#include <filesystem>

typedef unsigned int GLuint;

class Shader
{
public:
	Shader();
	void CompileAndLink();
	void Use();
	GLuint getShaderProgram() const;
	void setBool(const std::string& name, bool value) const;
	void setInt(const std::string& name, int value) const;
	void setFloat(const std::string& name, float value) const;
	void setMat4(const std::string& name, const float* mat) const;

private:
	std::string m_vertexShaderSource;
	std::string m_fragmentShaderSource;
	GLuint m_vertexShader;
	G<PERSON>uint m_fragmentShader;
	GLuint m_shaderProgram;
};

