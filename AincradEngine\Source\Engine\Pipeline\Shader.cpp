#include "Shader.h"

#include "glBuffer.h"
#include <filesystem>
#include <fstream>
#include <glad/glad.h>
#include <GLFW/glfw3.h>
#include <iostream>
#include <sstream>
#include <string>

Shader::Shader()
{
	// ## Load vertex shader source code from files ##
	std::ifstream ifsVertex("Shaders/VertexShader.glsl");
	std::ostringstream ossVertex;
	ossVertex << ifsVertex.rdbuf();
	m_vertexShaderSource = ossVertex.str();
	ifsVertex.close();
	ossVertex.clear();
	// ## Load fragment shader source code from files ##
	std::ifstream ifsFragment("Shaders/FragmentShader.glsl");
	std::ostringstream ossFragment;
	ossFragment << ifsFragment.rdbuf();
	m_fragmentShaderSource = ossFragment.str();
	ifsFragment.close();
	ossFragment.clear();
}

void Shader::CompileAndLink()
{
	int success;
	char infoLog[512];
	// ## Create vertex shader ##
	m_vertexShader = glCreateShader(GL_VERTEX_SHADER);
	// ## Convert std::string to const char* for OpenGL ##
	const char* vertexSourceCStr = m_vertexShaderSource.c_str();
	// ## Attach source code to shader object and compile ##
	glShaderSource(m_vertexShader, 1, &vertexSourceCStr, NULL);
	glCompileShader(m_vertexShader);

	glGetShaderiv(m_vertexShader, GL_COMPILE_STATUS, &success);
	if (!success)
	{
		glGetShaderInfoLog(m_vertexShader, 512, NULL, infoLog);
		std::cout << "ERROR::SHADER::VERTEX::COMPILATION_FAILED\n" << infoLog << std::endl;
	}
	// ## Create fragment shader ##
	m_fragmentShader = glCreateShader(GL_FRAGMENT_SHADER);
	// ## Convert std::string to const char* for OpenGL ##
	const char* fragmentSourceCStr = m_fragmentShaderSource.c_str();
	// ## Attach source code to shader object and compile ##
	glShaderSource(m_fragmentShader, 1, &fragmentSourceCStr, NULL);
	glCompileShader(m_fragmentShader);

	glGetShaderiv(m_fragmentShader, GL_COMPILE_STATUS, &success);
	if (!success)
	{
		glGetShaderInfoLog(m_fragmentShader, 512, NULL, infoLog);
		std::cout << "ERROR::SHADER::FRAGMENT::COMPILATION_FAILED\n" << infoLog << std::endl;
	}
	// ## Create shader program and link shaders ##
	m_shaderProgram = glCreateProgram();

	glAttachShader(m_shaderProgram, m_vertexShader);
	glAttachShader(m_shaderProgram, m_fragmentShader);
	// ## Link the program ##
	glLinkProgram(m_shaderProgram);

	glGetProgramiv(m_shaderProgram, GL_LINK_STATUS, &success);
	if (!success)
	{
		glGetProgramInfoLog(m_shaderProgram, 512, NULL, infoLog);
		std::cout << "ERROR::PROGRAM::LINKING_FAILED\n" << infoLog << std::endl;
	}
	// ## Shaders are linked into the program, we can delete them ##
	glDeleteShader(m_vertexShader);
	glDeleteShader(m_fragmentShader);
}
// ## Activate the shader program with shaders attached ##
void Shader::Use()
{
	glUseProgram(m_shaderProgram);
}

GLuint Shader::getShaderProgram() const
{
	return m_shaderProgram;
}
// ## Utility functions to set uniform variables in the shader ##
void Shader::setBool(const std::string& name, bool value) const
{
	glUniform1i(glGetUniformLocation(m_shaderProgram, name.c_str()), (int) value);
}

void Shader::setInt(const std::string& name, int value) const
{
	glUniform1i(glGetUniformLocation(m_shaderProgram, name.c_str()), value);
}

void Shader::setFloat(const std::string& name, float value) const
{
	glUniform1f(glGetUniformLocation(m_shaderProgram, name.c_str()), value);
}

void Shader::setMat4(const std::string& name, const float* mat) const
{
	glUniformMatrix4fv(glGetUniformLocation(m_shaderProgram, name.c_str()), 1, GL_FALSE, mat);
}
