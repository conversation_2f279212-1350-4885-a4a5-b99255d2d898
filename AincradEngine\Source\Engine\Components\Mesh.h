#pragma once

#include "../Pipeline/glBuffer.h"
#include "../Pipeline/VertexArray.h"

#include <glad/glad.h>
#include <GLFW/glfw3.h>

class Mesh
{
public:
	Mesh() = default;
	Mesh(const std::vector<float>& vertices);
	Mesh(const std::vector<float>& vertices, const std::vector<unsigned int>& indices);
	const void Draw() const;

private:
	glBuffer m_VBO;
	glBuffer m_EBO;
	VertexArray m_VAO;
	GLsizei m_vertexCount = 0;
	GLsizei m_indexCount = 0;
};

