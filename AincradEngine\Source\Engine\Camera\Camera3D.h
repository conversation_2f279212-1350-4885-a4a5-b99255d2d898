#pragma once

#include <glm/fwd.hpp>
#include <glm/glm.hpp>

class Camera3D
{
public:
	Camera3D();
	Camera3D(const glm::vec3& position);
	auto GetPosition() const -> const glm::vec3&;
	auto SetPosition(const glm::vec3& position) -> void;
	auto SetPosition(float x, float y, float z) -> void;
	auto GetViewMatrix() const -> const glm::mat4;
	auto MoveForward() -> void;
	auto MoveBackward() -> void;
	auto MoveLeft() -> void;
	auto MoveRight() -> void;

private:
	float m_cameraSpeed = 0.05f;

	glm::vec3 m_cameraPosition;
	glm::vec3 m_cameraTarget;
	glm::vec3 m_cameraDirection;
	glm::vec3 m_upVector;
	glm::vec3 m_cameraRight;
	glm::vec3 m_cameraUp;
	glm::vec3 m_cameraFront;

	glm::mat4 m_viewMatrix;
};

