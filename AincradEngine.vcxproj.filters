﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AincradEngine\Source\Main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Window\Window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Pipeline\Shader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Pipeline\glBuffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Pipeline\VertexArray.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Components\Mesh.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Debug\Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Components\Texture2D.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Components\Transform.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Camera\Camera3D.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Engine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AincradEngine\Source\Engine\Scenes\Scene.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AincradEngine\Source\Engine\Window\Window.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Pipeline\Shader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Pipeline\glBuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Pipeline\VertexArray.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Components\Mesh.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Debug\Debug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Components\Texture2D.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Components\Transform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Camera\Camera3D.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Engine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AincradEngine\Source\Engine\Scenes\Scene.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>