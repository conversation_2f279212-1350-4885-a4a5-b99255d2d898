#pragma once

#include <memory>
#include <string>
#include <utility>

struct GLFWwindow;

class Window
{
public:
	Window(int width, int heigth, const std::string& = "Aincrad Editor");
	~Window();
	auto WindowSettings() -> void;
	auto BeginFrame() -> void;
	auto EndFrame() -> void;
	auto GetWindowSize() const -> std::pair<float, float>;
	auto get() const -> GLFWwindow&;

	static auto WindowResizeCallback(GLFWwindow* window, int width, int height) -> void;

private:
	std::unique_ptr<GLFWwindow, void(*)(GLFWwindow*)> m_window;
	int m_width;
	int m_heigth;
	std::string m_title;
};

