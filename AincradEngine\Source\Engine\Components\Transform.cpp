#include "Transform.h"

#include <glm/gtc/matrix_transform.hpp>

Transform::Transform()
	: m_position(0.0f, 0.0f, 0.0f, 1.0f)
{
	m_position = {1.0f, 0.0f, 0.0f, 1.0f};
}

Transform::Transform(const glm::vec4 & position)
	: m_position(position)
{}

void Transform::SetPosition(const glm::vec4& position)
{
	m_position = position;
}

void Transform::SetPosition(float x, float y, float z, float w)
{
	m_position = {x, y, z, w};
}
